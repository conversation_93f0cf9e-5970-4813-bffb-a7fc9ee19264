"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/page",{

/***/ "(app-pages-browser)/./src/components/auth/LoginForm.tsx":
/*!*******************************************!*\
  !*** ./src/components/auth/LoginForm.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoginForm: () => (/* binding */ LoginForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ LoginForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Icons for enhanced UI\nconst EmailIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: \"w-5 h-5\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n            lineNumber: 18,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n        lineNumber: 17,\n        columnNumber: 3\n    }, undefined);\n_c = EmailIcon;\nconst PasswordIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: \"w-5 h-5\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n            lineNumber: 29,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n        lineNumber: 28,\n        columnNumber: 3\n    }, undefined);\n_c1 = PasswordIcon;\nconst ArrowRightIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: \"w-5 h-5\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M17 8l4 4m0 0l-4 4m4-4H3\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n            lineNumber: 40,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined);\n_c2 = ArrowRightIcon;\nconst LoginForm = (param)=>{\n    let { onSuccess, onSwitchToSignup } = param;\n    _s();\n    const { login } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: '',\n        password: ''\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.email) {\n            newErrors.email = 'Email is required';\n        } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            newErrors.email = 'Please enter a valid email address';\n        }\n        if (!formData.password) {\n            newErrors.password = 'Password is required';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        setLoading(true);\n        setErrors({});\n        try {\n            await login(formData.email, formData.password);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n        } catch (error) {\n            setErrors({\n                general: error instanceof Error ? error.message : 'Login failed'\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleInputChange = (field)=>(e)=>{\n            setFormData((prev)=>({\n                    ...prev,\n                    [field]: e.target.value\n                }));\n            if (errors[field]) {\n                setErrors((prev)=>({\n                        ...prev,\n                        [field]: undefined\n                    }));\n            }\n        };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.AuthCard, {\n        title: \"Welcome Back\",\n        subtitle: \"Sign in to your premium real estate account\",\n        className: \"w-full max-w-md mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-6\",\n                autoComplete: \"off\",\n                children: [\n                    errors.general && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-destructive/10 border border-destructive/20 rounded-xl animate-slide-in-left\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 text-destructive\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-destructive font-medium\",\n                                    children: errors.general\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                label: \"Email Address\",\n                                type: \"email\",\n                                value: formData.email,\n                                onChange: handleInputChange('email'),\n                                placeholder: \"Enter your email address\",\n                                autoComplete: \"new-email\",\n                                autoCorrect: \"off\",\n                                spellCheck: \"false\",\n                                error: errors.email,\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EmailIcon, {}, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 19\n                                }, void 0),\n                                variant: \"floating\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                label: \"Password\",\n                                type: \"password\",\n                                value: formData.password,\n                                onChange: handleInputChange('password'),\n                                placeholder: \"Enter your password\",\n                                autoComplete: \"new-password\",\n                                error: errors.password,\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PasswordIcon, {}, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 19\n                                }, void 0),\n                                variant: \"floating\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-right\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            className: \"text-sm text-lime-600 hover:text-lime-700 font-medium transition-colors\",\n                            children: \"Forgot your password?\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        type: \"submit\",\n                        className: \"w-full\",\n                        loading: loading,\n                        size: \"lg\",\n                        variant: \"gradient\",\n                        icon: !loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArrowRightIcon, {}, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 28\n                        }, void 0) : undefined,\n                        iconPosition: \"right\",\n                        children: loading ? 'Signing In...' : 'Sign In'\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative my-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full border-t border-slate-300\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex justify-center text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"px-4 bg-white text-slate-600 font-medium\",\n                            children: \"or\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    type: \"button\",\n                    variant: \"outline\",\n                    className: \"w-full border-slate-300 hover:border-lime-600\",\n                    size: \"lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5 mr-2\",\n                            viewBox: \"0 0 24 24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fill: \"#4285F4\",\n                                    d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fill: \"#34A853\",\n                                    d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fill: \"#FBBC05\",\n                                    d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fill: \"#EA4335\",\n                                    d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, undefined),\n                        \"Continue with Google\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-slate-700\",\n                    children: [\n                        \"Don't have an account?\",\n                        ' ',\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: onSwitchToSignup,\n                            className: \"text-blue-600 hover:text-blue-700 font-semibold transition-colors\",\n                            children: \"Create your account\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LoginForm, \"hIkPsJa2IBAqTAcnQJd8fCbBLSI=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c3 = LoginForm;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"EmailIcon\");\n$RefreshReg$(_c1, \"PasswordIcon\");\n$RefreshReg$(_c2, \"ArrowRightIcon\");\n$RefreshReg$(_c3, \"LoginForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2F1dGgvTG9naW5Gb3JtLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRXVDO0FBQ1E7QUFDRjtBQUNFO0FBQ0M7QUFRaEQsd0JBQXdCO0FBQ3hCLE1BQU1NLFlBQVksa0JBQ2hCLDhEQUFDQztRQUFJQyxXQUFVO1FBQVVDLE1BQUs7UUFBT0MsUUFBTztRQUFlQyxTQUFRO2tCQUNqRSw0RUFBQ0M7WUFDQ0MsZUFBYztZQUNkQyxnQkFBZTtZQUNmQyxhQUFhO1lBQ2JDLEdBQUU7Ozs7Ozs7Ozs7O0tBTkZWO0FBV04sTUFBTVcsZUFBZSxrQkFDbkIsOERBQUNWO1FBQUlDLFdBQVU7UUFBVUMsTUFBSztRQUFPQyxRQUFPO1FBQWVDLFNBQVE7a0JBQ2pFLDRFQUFDQztZQUNDQyxlQUFjO1lBQ2RDLGdCQUFlO1lBQ2ZDLGFBQWE7WUFDYkMsR0FBRTs7Ozs7Ozs7Ozs7TUFORkM7QUFXTixNQUFNQyxpQkFBaUIsa0JBQ3JCLDhEQUFDWDtRQUFJQyxXQUFVO1FBQVVDLE1BQUs7UUFBT0MsUUFBTztRQUFlQyxTQUFRO2tCQUNqRSw0RUFBQ0M7WUFBS0MsZUFBYztZQUFRQyxnQkFBZTtZQUFRQyxhQUFhO1lBQUdDLEdBQUU7Ozs7Ozs7Ozs7O01BRm5FRTtBQU1DLE1BQU1DLFlBQXNDO1FBQUMsRUFBRUMsU0FBUyxFQUFFQyxnQkFBZ0IsRUFBRTs7SUFDakYsTUFBTSxFQUFFQyxLQUFLLEVBQUUsR0FBR2pCLDhEQUFPQTtJQUN6QixNQUFNLENBQUNrQixVQUFVQyxZQUFZLEdBQUd2QiwrQ0FBUUEsQ0FBWTtRQUNsRHdCLE9BQU87UUFDUEMsVUFBVTtJQUNaO0lBQ0EsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUczQiwrQ0FBUUEsQ0FBMkMsQ0FBQztJQUNoRixNQUFNLENBQUM0QixTQUFTQyxXQUFXLEdBQUc3QiwrQ0FBUUEsQ0FBQztJQUV2QyxNQUFNOEIsZUFBZTtRQUNuQixNQUFNQyxZQUEyQixDQUFDO1FBRWxDLElBQUksQ0FBQ1QsU0FBU0UsS0FBSyxFQUFFO1lBQ25CTyxVQUFVUCxLQUFLLEdBQUc7UUFDcEIsT0FBTyxJQUFJLENBQUMsZUFBZVEsSUFBSSxDQUFDVixTQUFTRSxLQUFLLEdBQUc7WUFDL0NPLFVBQVVQLEtBQUssR0FBRztRQUNwQjtRQUVBLElBQUksQ0FBQ0YsU0FBU0csUUFBUSxFQUFFO1lBQ3RCTSxVQUFVTixRQUFRLEdBQUc7UUFDdkI7UUFFQUUsVUFBVUk7UUFDVixPQUFPRSxPQUFPQyxJQUFJLENBQUNILFdBQVdJLE1BQU0sS0FBSztJQUMzQztJQUVBLE1BQU1DLGVBQWUsT0FBT0M7UUFDMUJBLEVBQUVDLGNBQWM7UUFFaEIsSUFBSSxDQUFDUixnQkFBZ0I7UUFFckJELFdBQVc7UUFDWEYsVUFBVSxDQUFDO1FBRVgsSUFBSTtZQUNGLE1BQU1OLE1BQU1DLFNBQVNFLEtBQUssRUFBRUYsU0FBU0csUUFBUTtZQUM3Q04sc0JBQUFBLGdDQUFBQTtRQUNGLEVBQUUsT0FBT29CLE9BQU87WUFDZFosVUFBVTtnQkFBRWEsU0FBU0QsaUJBQWlCRSxRQUFRRixNQUFNRyxPQUFPLEdBQUc7WUFBZTtRQUMvRSxTQUFVO1lBQ1JiLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTWMsb0JBQW9CLENBQUNDLFFBQTJCLENBQUNQO1lBQ3JEZCxZQUFZc0IsQ0FBQUEsT0FBUztvQkFBRSxHQUFHQSxJQUFJO29CQUFFLENBQUNELE1BQU0sRUFBRVAsRUFBRVMsTUFBTSxDQUFDQyxLQUFLO2dCQUFDO1lBQ3hELElBQUlyQixNQUFNLENBQUNrQixNQUFNLEVBQUU7Z0JBQ2pCakIsVUFBVWtCLENBQUFBLE9BQVM7d0JBQUUsR0FBR0EsSUFBSTt3QkFBRSxDQUFDRCxNQUFNLEVBQUVJO29CQUFVO1lBQ25EO1FBQ0Y7SUFFQSxxQkFDRSw4REFBQzdDLHlEQUFRQTtRQUNQOEMsT0FBTTtRQUNOQyxVQUFTO1FBQ1QzQyxXQUFVOzswQkFFViw4REFBQzRDO2dCQUFLQyxVQUFVaEI7Z0JBQWM3QixXQUFVO2dCQUFZOEMsY0FBYTs7b0JBQzlEM0IsT0FBT2MsT0FBTyxrQkFDYiw4REFBQ2M7d0JBQUkvQyxXQUFVO2tDQUNiLDRFQUFDK0M7NEJBQUkvQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7b0NBQTJCQyxNQUFLO29DQUFlRSxTQUFROzhDQUNwRSw0RUFBQ0M7d0NBQ0M0QyxVQUFTO3dDQUNUeEMsR0FBRTt3Q0FDRnlDLFVBQVM7Ozs7Ozs7Ozs7OzhDQUdiLDhEQUFDQztvQ0FBRWxELFdBQVU7OENBQXdDbUIsT0FBT2MsT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBS3pFLDhEQUFDYzt3QkFBSS9DLFdBQVU7OzBDQUNiLDhEQUFDTCx1REFBS0E7Z0NBQ0p3RCxPQUFNO2dDQUNOQyxNQUFLO2dDQUNMWixPQUFPekIsU0FBU0UsS0FBSztnQ0FDckJvQyxVQUFVakIsa0JBQWtCO2dDQUM1QmtCLGFBQVk7Z0NBQ1pSLGNBQWE7Z0NBQ2JTLGFBQVk7Z0NBQ1pDLFlBQVc7Z0NBQ1h4QixPQUFPYixPQUFPRixLQUFLO2dDQUNuQndDLG9CQUFNLDhEQUFDM0Q7Ozs7O2dDQUNQNEQsU0FBUTtnQ0FDUkMsUUFBUTs7Ozs7OzBDQUdWLDhEQUFDaEUsdURBQUtBO2dDQUNKd0QsT0FBTTtnQ0FDTkMsTUFBSztnQ0FDTFosT0FBT3pCLFNBQVNHLFFBQVE7Z0NBQ3hCbUMsVUFBVWpCLGtCQUFrQjtnQ0FDNUJrQixhQUFZO2dDQUNaUixjQUFhO2dDQUNiZCxPQUFPYixPQUFPRCxRQUFRO2dDQUN0QnVDLG9CQUFNLDhEQUFDaEQ7Ozs7O2dDQUNQaUQsU0FBUTtnQ0FDUkMsUUFBUTs7Ozs7Ozs7Ozs7O2tDQUtaLDhEQUFDWjt3QkFBSS9DLFdBQVU7a0NBQ2IsNEVBQUM0RDs0QkFBT1IsTUFBSzs0QkFBU3BELFdBQVU7c0NBQTBFOzs7Ozs7Ozs7OztrQ0FLNUcsOERBQUNOLHlEQUFNQTt3QkFDTDBELE1BQUs7d0JBQ0xwRCxXQUFVO3dCQUNWcUIsU0FBU0E7d0JBQ1R3QyxNQUFLO3dCQUNMSCxTQUFRO3dCQUNSRCxNQUFNLENBQUNwQyx3QkFBVSw4REFBQ1g7Ozs7cUNBQW9CK0I7d0JBQ3RDcUIsY0FBYTtrQ0FFWnpDLFVBQVUsa0JBQWtCOzs7Ozs7Ozs7Ozs7MEJBS2pDLDhEQUFDMEI7Z0JBQUkvQyxXQUFVOztrQ0FDYiw4REFBQytDO3dCQUFJL0MsV0FBVTtrQ0FDYiw0RUFBQytDOzRCQUFJL0MsV0FBVTs7Ozs7Ozs7Ozs7a0NBRWpCLDhEQUFDK0M7d0JBQUkvQyxXQUFVO2tDQUNiLDRFQUFDK0Q7NEJBQUsvRCxXQUFVO3NDQUEyQzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBSy9ELDhEQUFDK0M7Z0JBQUkvQyxXQUFVOzBCQUNiLDRFQUFDTix5REFBTUE7b0JBQUMwRCxNQUFLO29CQUFTTSxTQUFRO29CQUFVMUQsV0FBVTtvQkFBZ0Q2RCxNQUFLOztzQ0FDckcsOERBQUM5RDs0QkFBSUMsV0FBVTs0QkFBZUcsU0FBUTs7OENBQ3BDLDhEQUFDQztvQ0FDQ0gsTUFBSztvQ0FDTE8sR0FBRTs7Ozs7OzhDQUVKLDhEQUFDSjtvQ0FDQ0gsTUFBSztvQ0FDTE8sR0FBRTs7Ozs7OzhDQUVKLDhEQUFDSjtvQ0FDQ0gsTUFBSztvQ0FDTE8sR0FBRTs7Ozs7OzhDQUVKLDhEQUFDSjtvQ0FDQ0gsTUFBSztvQ0FDTE8sR0FBRTs7Ozs7Ozs7Ozs7O3dCQUVBOzs7Ozs7Ozs7Ozs7MEJBTVYsOERBQUN1QztnQkFBSS9DLFdBQVU7MEJBQ2IsNEVBQUNrRDtvQkFBRWxELFdBQVU7O3dCQUF5Qjt3QkFDUjtzQ0FDNUIsOERBQUM0RDs0QkFDQ1IsTUFBSzs0QkFDTFksU0FBU25EOzRCQUNUYixXQUFVO3NDQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9YLEVBQUM7R0E3S1lXOztRQUNPZCwwREFBT0E7OztNQURkYyIsInNvdXJjZXMiOlsiRTpcXENvZGVcXFBvcnRmb2xpb1xcTmV3TVJIXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFxhdXRoXFxMb2dpbkZvcm0udHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9CdXR0b24nXG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9JbnB1dCdcbmltcG9ydCB7IEF1dGhDYXJkIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL0NhcmQnXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCdcbmltcG9ydCB7IExvZ2luRGF0YSB9IGZyb20gJ0AvdHlwZXMvYXV0aCdcblxuaW50ZXJmYWNlIExvZ2luRm9ybVByb3BzIHtcbiAgb25TdWNjZXNzPzogKCkgPT4gdm9pZFxuICBvblN3aXRjaFRvU2lnbnVwPzogKCkgPT4gdm9pZFxufVxuXG4vLyBJY29ucyBmb3IgZW5oYW5jZWQgVUlcbmNvbnN0IEVtYWlsSWNvbiA9ICgpID0+IChcbiAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgPHBhdGhcbiAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgZD1cIk0xNiAxMmE0IDQgMCAxMC04IDAgNCA0IDAgMDA4IDB6bTAgMHYxLjVhMi41IDIuNSAwIDAwNSAwVjEyYTkgOSAwIDEwLTkgOW00LjUtMS4yMDZhOC45NTkgOC45NTkgMCAwMS00LjUgMS4yMDdcIlxuICAgIC8+XG4gIDwvc3ZnPlxuKVxuXG5jb25zdCBQYXNzd29yZEljb24gPSAoKSA9PiAoXG4gIDxzdmcgY2xhc3NOYW1lPVwidy01IGgtNVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgIDxwYXRoXG4gICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICBzdHJva2VXaWR0aD17Mn1cbiAgICAgIGQ9XCJNMTIgMTV2Mm0tNiA0aDEyYTIgMiAwIDAwMi0ydi02YTIgMiAwIDAwLTItMkg2YTIgMiAwIDAwLTIgMnY2YTIgMiAwIDAwMiAyem0xMC0xMFY3YTQgNCAwIDAwLTggMHY0aDh6XCJcbiAgICAvPlxuICA8L3N2Zz5cbilcblxuY29uc3QgQXJyb3dSaWdodEljb24gPSAoKSA9PiAoXG4gIDxzdmcgY2xhc3NOYW1lPVwidy01IGgtNVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xNyA4bDQgNG0wIDBsLTQgNG00LTRIM1wiIC8+XG4gIDwvc3ZnPlxuKVxuXG5leHBvcnQgY29uc3QgTG9naW5Gb3JtOiBSZWFjdC5GQzxMb2dpbkZvcm1Qcm9wcz4gPSAoeyBvblN1Y2Nlc3MsIG9uU3dpdGNoVG9TaWdudXAgfSkgPT4ge1xuICBjb25zdCB7IGxvZ2luIH0gPSB1c2VBdXRoKClcbiAgY29uc3QgW2Zvcm1EYXRhLCBzZXRGb3JtRGF0YV0gPSB1c2VTdGF0ZTxMb2dpbkRhdGE+KHtcbiAgICBlbWFpbDogJycsXG4gICAgcGFzc3dvcmQ6ICcnXG4gIH0pXG4gIGNvbnN0IFtlcnJvcnMsIHNldEVycm9yc10gPSB1c2VTdGF0ZTxQYXJ0aWFsPExvZ2luRGF0YSAmIHsgZ2VuZXJhbDogc3RyaW5nIH0+Pih7fSlcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgY29uc3QgdmFsaWRhdGVGb3JtID0gKCk6IGJvb2xlYW4gPT4ge1xuICAgIGNvbnN0IG5ld0Vycm9yczogdHlwZW9mIGVycm9ycyA9IHt9XG5cbiAgICBpZiAoIWZvcm1EYXRhLmVtYWlsKSB7XG4gICAgICBuZXdFcnJvcnMuZW1haWwgPSAnRW1haWwgaXMgcmVxdWlyZWQnXG4gICAgfSBlbHNlIGlmICghL1xcUytAXFxTK1xcLlxcUysvLnRlc3QoZm9ybURhdGEuZW1haWwpKSB7XG4gICAgICBuZXdFcnJvcnMuZW1haWwgPSAnUGxlYXNlIGVudGVyIGEgdmFsaWQgZW1haWwgYWRkcmVzcydcbiAgICB9XG5cbiAgICBpZiAoIWZvcm1EYXRhLnBhc3N3b3JkKSB7XG4gICAgICBuZXdFcnJvcnMucGFzc3dvcmQgPSAnUGFzc3dvcmQgaXMgcmVxdWlyZWQnXG4gICAgfVxuXG4gICAgc2V0RXJyb3JzKG5ld0Vycm9ycylcbiAgICByZXR1cm4gT2JqZWN0LmtleXMobmV3RXJyb3JzKS5sZW5ndGggPT09IDBcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IGFzeW5jIChlOiBSZWFjdC5Gb3JtRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KClcblxuICAgIGlmICghdmFsaWRhdGVGb3JtKCkpIHJldHVyblxuXG4gICAgc2V0TG9hZGluZyh0cnVlKVxuICAgIHNldEVycm9ycyh7fSlcblxuICAgIHRyeSB7XG4gICAgICBhd2FpdCBsb2dpbihmb3JtRGF0YS5lbWFpbCwgZm9ybURhdGEucGFzc3dvcmQpXG4gICAgICBvblN1Y2Nlc3M/LigpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHNldEVycm9ycyh7IGdlbmVyYWw6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ0xvZ2luIGZhaWxlZCcgfSlcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVJbnB1dENoYW5nZSA9IChmaWVsZDoga2V5b2YgTG9naW5EYXRhKSA9PiAoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcbiAgICBzZXRGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIFtmaWVsZF06IGUudGFyZ2V0LnZhbHVlIH0pKVxuICAgIGlmIChlcnJvcnNbZmllbGRdKSB7XG4gICAgICBzZXRFcnJvcnMocHJldiA9PiAoeyAuLi5wcmV2LCBbZmllbGRdOiB1bmRlZmluZWQgfSkpXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8QXV0aENhcmRcbiAgICAgIHRpdGxlPVwiV2VsY29tZSBCYWNrXCJcbiAgICAgIHN1YnRpdGxlPVwiU2lnbiBpbiB0byB5b3VyIHByZW1pdW0gcmVhbCBlc3RhdGUgYWNjb3VudFwiXG4gICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgbWF4LXctbWQgbXgtYXV0b1wiXG4gICAgPlxuICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdH0gY2xhc3NOYW1lPVwic3BhY2UteS02XCIgYXV0b0NvbXBsZXRlPVwib2ZmXCI+XG4gICAgICAgIHtlcnJvcnMuZ2VuZXJhbCAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYmctZGVzdHJ1Y3RpdmUvMTAgYm9yZGVyIGJvcmRlci1kZXN0cnVjdGl2ZS8yMCByb3VuZGVkLXhsIGFuaW1hdGUtc2xpZGUtaW4tbGVmdFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1kZXN0cnVjdGl2ZVwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XG4gICAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICAgIGZpbGxSdWxlPVwiZXZlbm9kZFwiXG4gICAgICAgICAgICAgICAgICBkPVwiTTE4IDEwYTggOCAwIDExLTE2IDAgOCA4IDAgMDExNiAwem0tNyA0YTEgMSAwIDExLTIgMCAxIDEgMCAwMTIgMHptLTEtOWExIDEgMCAwMC0xIDF2NGExIDEgMCAxMDIgMFY2YTEgMSAwIDAwLTEtMXpcIlxuICAgICAgICAgICAgICAgICAgY2xpcFJ1bGU9XCJldmVub2RkXCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWRlc3RydWN0aXZlIGZvbnQtbWVkaXVtXCI+e2Vycm9ycy5nZW5lcmFsfTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS01XCI+XG4gICAgICAgICAgPElucHV0XG4gICAgICAgICAgICBsYWJlbD1cIkVtYWlsIEFkZHJlc3NcIlxuICAgICAgICAgICAgdHlwZT1cImVtYWlsXCJcbiAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5lbWFpbH1cbiAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZSgnZW1haWwnKX1cbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBlbWFpbCBhZGRyZXNzXCJcbiAgICAgICAgICAgIGF1dG9Db21wbGV0ZT1cIm5ldy1lbWFpbFwiXG4gICAgICAgICAgICBhdXRvQ29ycmVjdD1cIm9mZlwiXG4gICAgICAgICAgICBzcGVsbENoZWNrPVwiZmFsc2VcIlxuICAgICAgICAgICAgZXJyb3I9e2Vycm9ycy5lbWFpbH1cbiAgICAgICAgICAgIGljb249ezxFbWFpbEljb24gLz59XG4gICAgICAgICAgICB2YXJpYW50PVwiZmxvYXRpbmdcIlxuICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAvPlxuXG4gICAgICAgICAgPElucHV0XG4gICAgICAgICAgICBsYWJlbD1cIlBhc3N3b3JkXCJcbiAgICAgICAgICAgIHR5cGU9XCJwYXNzd29yZFwiXG4gICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucGFzc3dvcmR9XG4gICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2UoJ3Bhc3N3b3JkJyl9XG4gICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHlvdXIgcGFzc3dvcmRcIlxuICAgICAgICAgICAgYXV0b0NvbXBsZXRlPVwibmV3LXBhc3N3b3JkXCJcbiAgICAgICAgICAgIGVycm9yPXtlcnJvcnMucGFzc3dvcmR9XG4gICAgICAgICAgICBpY29uPXs8UGFzc3dvcmRJY29uIC8+fVxuICAgICAgICAgICAgdmFyaWFudD1cImZsb2F0aW5nXCJcbiAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgLz5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEZvcmdvdCBQYXNzd29yZCBMaW5rICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cbiAgICAgICAgICA8YnV0dG9uIHR5cGU9XCJidXR0b25cIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbGltZS02MDAgaG92ZXI6dGV4dC1saW1lLTcwMCBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgRm9yZ290IHlvdXIgcGFzc3dvcmQ/XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxCdXR0b25cbiAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGxcIlxuICAgICAgICAgIGxvYWRpbmc9e2xvYWRpbmd9XG4gICAgICAgICAgc2l6ZT1cImxnXCJcbiAgICAgICAgICB2YXJpYW50PVwiZ3JhZGllbnRcIlxuICAgICAgICAgIGljb249eyFsb2FkaW5nID8gPEFycm93UmlnaHRJY29uIC8+IDogdW5kZWZpbmVkfVxuICAgICAgICAgIGljb25Qb3NpdGlvbj1cInJpZ2h0XCJcbiAgICAgICAgPlxuICAgICAgICAgIHtsb2FkaW5nID8gJ1NpZ25pbmcgSW4uLi4nIDogJ1NpZ24gSW4nfVxuICAgICAgICA8L0J1dHRvbj5cbiAgICAgIDwvZm9ybT5cblxuICAgICAgey8qIERpdmlkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIG15LThcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgYm9yZGVyLXQgYm9yZGVyLXNsYXRlLTMwMFwiPjwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBmbGV4IGp1c3RpZnktY2VudGVyIHRleHQtc21cIj5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJweC00IGJnLXdoaXRlIHRleHQtc2xhdGUtNjAwIGZvbnQtbWVkaXVtXCI+b3I8L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBTb2NpYWwgTG9naW4gT3B0aW9ucyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgIDxCdXR0b24gdHlwZT1cImJ1dHRvblwiIHZhcmlhbnQ9XCJvdXRsaW5lXCIgY2xhc3NOYW1lPVwidy1mdWxsIGJvcmRlci1zbGF0ZS0zMDAgaG92ZXI6Ym9yZGVyLWxpbWUtNjAwXCIgc2l6ZT1cImxnXCI+XG4gICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01IG1yLTJcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICBmaWxsPVwiIzQyODVGNFwiXG4gICAgICAgICAgICAgIGQ9XCJNMjIuNTYgMTIuMjVjMC0uNzgtLjA3LTEuNTMtLjItMi4yNUgxMnY0LjI2aDUuOTJjLS4yNiAxLjM3LTEuMDQgMi41My0yLjIxIDMuMzF2Mi43N2gzLjU3YzIuMDgtMS45MiAzLjI4LTQuNzQgMy4yOC04LjA5elwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgZmlsbD1cIiMzNEE4NTNcIlxuICAgICAgICAgICAgICBkPVwiTTEyIDIzYzIuOTcgMCA1LjQ2LS45OCA3LjI4LTIuNjZsLTMuNTctMi43N2MtLjk4LjY2LTIuMjMgMS4wNi0zLjcxIDEuMDYtMi44NiAwLTUuMjktMS45My02LjE2LTQuNTNIMi4xOHYyLjg0QzMuOTkgMjAuNTMgNy43IDIzIDEyIDIzelwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgZmlsbD1cIiNGQkJDMDVcIlxuICAgICAgICAgICAgICBkPVwiTTUuODQgMTQuMDljLS4yMi0uNjYtLjM1LTEuMzYtLjM1LTIuMDlzLjEzLTEuNDMuMzUtMi4wOVY3LjA3SDIuMThDMS40MyA4LjU1IDEgMTAuMjIgMSAxMnMuNDMgMy40NSAxLjE4IDQuOTNsMi44NS0yLjIyLjgxLS42MnpcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgIGZpbGw9XCIjRUE0MzM1XCJcbiAgICAgICAgICAgICAgZD1cIk0xMiA1LjM4YzEuNjIgMCAzLjA2LjU2IDQuMjEgMS42NGwzLjE1LTMuMTVDMTcuNDUgMi4wOSAxNC45NyAxIDEyIDEgNy43IDEgMy45OSAzLjQ3IDIuMTggNy4wN2wzLjY2IDIuODRjLjg3LTIuNiAzLjMtNC41MyA2LjE2LTQuNTN6XCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgQ29udGludWUgd2l0aCBHb29nbGVcbiAgICAgICAgPC9CdXR0b24+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFN3aXRjaCB0byBTaWdudXAgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTggdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXNsYXRlLTcwMFwiPlxuICAgICAgICAgIERvbiZhcG9zO3QgaGF2ZSBhbiBhY2NvdW50P3snICd9XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICBvbkNsaWNrPXtvblN3aXRjaFRvU2lnbnVwfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMCBob3Zlcjp0ZXh0LWJsdWUtNzAwIGZvbnQtc2VtaWJvbGQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIENyZWF0ZSB5b3VyIGFjY291bnRcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9wPlxuICAgICAgPC9kaXY+XG4gICAgPC9BdXRoQ2FyZD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJCdXR0b24iLCJJbnB1dCIsIkF1dGhDYXJkIiwidXNlQXV0aCIsIkVtYWlsSWNvbiIsInN2ZyIsImNsYXNzTmFtZSIsImZpbGwiLCJzdHJva2UiLCJ2aWV3Qm94IiwicGF0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIiwiZCIsIlBhc3N3b3JkSWNvbiIsIkFycm93UmlnaHRJY29uIiwiTG9naW5Gb3JtIiwib25TdWNjZXNzIiwib25Td2l0Y2hUb1NpZ251cCIsImxvZ2luIiwiZm9ybURhdGEiLCJzZXRGb3JtRGF0YSIsImVtYWlsIiwicGFzc3dvcmQiLCJlcnJvcnMiLCJzZXRFcnJvcnMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInZhbGlkYXRlRm9ybSIsIm5ld0Vycm9ycyIsInRlc3QiLCJPYmplY3QiLCJrZXlzIiwibGVuZ3RoIiwiaGFuZGxlU3VibWl0IiwiZSIsInByZXZlbnREZWZhdWx0IiwiZXJyb3IiLCJnZW5lcmFsIiwiRXJyb3IiLCJtZXNzYWdlIiwiaGFuZGxlSW5wdXRDaGFuZ2UiLCJmaWVsZCIsInByZXYiLCJ0YXJnZXQiLCJ2YWx1ZSIsInVuZGVmaW5lZCIsInRpdGxlIiwic3VidGl0bGUiLCJmb3JtIiwib25TdWJtaXQiLCJhdXRvQ29tcGxldGUiLCJkaXYiLCJmaWxsUnVsZSIsImNsaXBSdWxlIiwicCIsImxhYmVsIiwidHlwZSIsIm9uQ2hhbmdlIiwicGxhY2Vob2xkZXIiLCJhdXRvQ29ycmVjdCIsInNwZWxsQ2hlY2siLCJpY29uIiwidmFyaWFudCIsInJlcXVpcmVkIiwiYnV0dG9uIiwic2l6ZSIsImljb25Qb3NpdGlvbiIsInNwYW4iLCJvbkNsaWNrIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/LoginForm.tsx\n"));

/***/ })

});